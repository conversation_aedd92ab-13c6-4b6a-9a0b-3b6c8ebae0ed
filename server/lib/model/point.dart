class Point {
  Point(this.x, this.y, this.pressure, this.color);

  final double x;
  final double y;
  final double pressure;
  final String color;

  static Point fromJson(Map<String, dynamic> json) {
    return Point(
      json['x'],
      json['y'],
      json['pressure'],
      json['color'],
    );
  }

  double get strokeWidth {
    final int multiplier;
    if (pressure < 0.5) {
      multiplier = 4;
    } else if (pressure < 0.7) {
      multiplier = 8;
    } else {
      multiplier = 10;
    }

    return multiplier * pressure;
  }

  @override
  String toString() {
    return "Point(x: $x, y: $y, color: $color)";
  }
}
