import 'dart:io';

import 'package:flutter/widgets.dart';
import 'package:flutter_acrylic/flutter_acrylic.dart';
import 'package:draw_it/main.dart';
import 'package:window_manager/window_manager.dart';
import 'package:window_size/window_size.dart';

const _tag = '[window]';

class WindowHandler {
  const WindowHandler();

  Future<void> removeTransparency() async {
    if (Platform.isLinux) {
      log.d('$_tag Linux does not support window transparency');
      return;
    }

    try {
      await Window.setEffect(effect: WindowEffect.disabled);

      // Disable transparency
      Window.setWindowBackgroundColorToDefaultColor();
      Window.makeTitlebarOpaque();
      Window.removeMaskImage();
      Window.enableShadow();

      Window.addToolbar();
      await Window.showWindowControls();
      await Window.showTitle();

      if (await windowManager.isAlwaysOnTop()) {
        await windowManager.setAlwaysOnTop(false);
      }
    } catch (e, stack) {
      log.e('$_tag Error: $e', error: e, stackTrace: stack);
    }
  }

  Future<void> addTransparency() async {
    if (Platform.isLinux) {
      log.d('$_tag Linux does not support window transparency');
      return;
    }

    try {
      await Window.setEffect(effect: WindowEffect.transparent);
      Window.makeWindowFullyTransparent();

      // Allow click-through
      await Window.ignoreMouseEvents();

      if (!Platform.isWindows) {
        log.d('$_tag windows and linux does not support window controls');
        // On windows the controls is still there but it does not show the
        // close buttons making it confusing to the user
        await Window.hideWindowControls();
        await Window.removeToolbar();
      }

      await windowManager.center();
      await Window.enableFullSizeContentView();

      await fillScreen();

      await windowManager.setResizable(false);
      if (!Platform.isWindows) {
        log.d('$_tag windows and linux does not support movable');
        await windowManager.setMovable(false);
      }
    } catch (e, stack) {
      log.e('Error: $e', error: e, stackTrace: stack);
    }
  }

  Future<void> fillScreen() async {
    if (Platform.isLinux) {
      await WindowManager.instance.maximize(vertically: true);
    } else {
      try {
        final window = await getWindowInfo();
        final frame = window.screen?.frame;
        if (frame != null) {
          setWindowFrame(Rect.fromLTWH(0, 0, frame.width, frame.height));
        }
      } catch (e, stack) {
        log.e('Error: $e', error: e, stackTrace: stack);
      }
    }
  }

  Future<void> disableDrag() async {
    if (Platform.isLinux) {
      log.d('$_tag Linux does not support window drag');
      return;
    }
    try {
      return windowManager.ensureInitialized();
    } catch (e, stack) {
      log.e('Error: $e', error: e, stackTrace: stack);
    }
  }

  Future<void> show() async {
    try {
      WidgetsFlutterBinding.ensureInitialized();
      await windowManager.ensureInitialized();

      await windowManager.show();

      if (!await windowManager.isFocused()) {
        await windowManager.focus();
      }

      if (!await windowManager.isAlwaysOnTop()) {
        await windowManager.setAlwaysOnTop(true);
      }

      await windowManager.setMinimizable(false);
      await fillScreen();
    } catch (e, stack) {
      log.e('Error: $e', error: e, stackTrace: stack);
    }
  }

  Future<void> hide() async {
    try {
      WidgetsFlutterBinding.ensureInitialized();
      await windowManager.ensureInitialized();

      await windowManager.hide();

      await windowManager.setAlwaysOnTop(false);
    } catch (e, stack) {
      log.e('Error: $e', error: e, stackTrace: stack);
    }
  }
}
