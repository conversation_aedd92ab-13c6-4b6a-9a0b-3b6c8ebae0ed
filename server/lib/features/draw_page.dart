import 'dart:async';
import 'dart:io';

import 'package:bonsoir/bonsoir.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:draw_it/common/di.dart';
import 'package:draw_it/common/network_discovery.dart';
import 'package:draw_it/common/window.dart';
import 'package:draw_it/main.dart';
import 'package:draw_it/model/point.dart';
import 'package:draw_it/model/screen_command.dart';

const handler = WindowHandler();

class DrawPage extends StatefulWidget {
  const DrawPage({super.key});

  @override
  State<DrawPage> createState() => _DrawPageState();
}

class _DrawPageState extends State<DrawPage> {
  final List<List<Point>> _points = [];
  final CurrentDrawing _currentDrawing = CurrentDrawing();
  final NetworkDiscovery _networkDiscovery = DiModule.networkDiscovery();

  Size _screenSize = const Size(1920, 1080);
  final _drawColor = Colors.red;
  Color _backgroundColor = Colors.transparent;

  StreamSubscription<ScreenCommand>? _serverSubscription;

  @override
  void initState() {
    _startServer();

    handler.show();
    handler.addTransparency();

    if (Platform.isLinux) {
      _handleAction(ShowWhiteboardCommand());
    }

    HardwareKeyboard.instance.addHandler(_listenKeystroke);

    super.initState();
  }

  @override
  void dispose() {
    HardwareKeyboard.instance.removeHandler(_listenKeystroke);
    _serverSubscription?.cancel();
    _networkDiscovery.stop();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Size targetSize = MediaQuery.sizeOf(context); // Target screen size

    return Container(
      color: _backgroundColor,
      child: DottedBorder(
        color: Colors.blue,
        strokeWidth: 6,
        dashPattern: const [12],
        child: CustomPaint(
          painter: DrawPainter(
            _points + [_currentDrawing.points],
            _screenSize,
            targetSize,
            _drawColor,
          ),
        ),
      ),
    );
  }

  Future<void> _startServer() async {
    final pointRepository = DiModule.pointRepository();

    _serverSubscription = pointRepository.points.listen(_handleAction);
    await _networkDiscovery.advertise();
  }

  void _handleAction(ScreenCommand action) {
    log.i('[action] $action');

    switch (action) {
      case CleanCommand():
        setState(() {
          _points.clear();
        });
      case HideWindowCommand():
        handler.hide();
      case ShowWindowCommand():
        handler.show();
      case ShowWhiteboardCommand():
        handler.show();
        handler.removeTransparency();
        setState(() {
          _backgroundColor = Colors.white;
        });
      case HideWhiteboardCommand():
        handler.show();
        handler.addTransparency();
        setState(() {
          _backgroundColor = Colors.transparent;
        });
      case DrawCommand():
        handler.show();

        setState(() {
          _screenSize = action.screenSize;
          _points.add(action.points);
        });
      case DrawSingleCommand():
        setState(() {
          _screenSize = action.screenSize;
          _currentDrawing.addPoint(action.drawId, action.point);
        });
        break;
      case TouchUpCommand():
        setState(() {
          _points.add(List.of(_currentDrawing.points));
          _currentDrawing.clear(action.drawId);
        });
        break;
      case TouchDownCommand():
        handler.show();
        break;
    }
  }

  bool _listenKeystroke(KeyEvent event) {
    if (event is KeyDownEvent) {
      if (event.logicalKey == LogicalKeyboardKey.escape) {
        handler.hide();

        return true;
      }
    }

    return false;
  }
}

class DrawPainter extends CustomPainter {
  DrawPainter(this.points, this.originSize, this.targetSize, Color drawColor)
      : stylusPaint = Paint()
          ..color = drawColor
          ..strokeWidth = 4
          ..strokeCap = StrokeCap.round;

  final Size originSize;
  final Size targetSize;
  final List<List<Point>> points;
  final Paint stylusPaint;

  @override
  void paint(Canvas canvas, Size size) {
    log.d('painting ${points.length} group of points');

    for (final pointList in points) {
      for (var i = 0; i < pointList.length - 1; i++) {
        final p1 = pointList[i];
        final p2 = pointList[i + 1];

        stylusPaint.color = Color(int.parse(p1.color.replaceFirst('#', '0x')));

        final int multiplier;
        if (p1.pressure < 0.5) {
          multiplier = 4;
        } else if (p1.pressure < 0.7) {
          multiplier = 8;
        } else {
          multiplier = 10;
        }
        stylusPaint.strokeWidth = multiplier * p1.pressure;

        canvas.drawLine(_transform(p1), _transform(p2), stylusPaint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant DrawPainter oldDelegate) {
    return true;
  }

  Offset _transform(Point originalPoint) {
    double newX = (originalPoint.x / originSize.width) * targetSize.width;
    double newY = (originalPoint.y / originSize.height) * targetSize.height;

    return Offset(newX, newY);
  }
}

class CurrentDrawing {
  CurrentDrawing([
    this._drawId,
    this.screenSize,
  ]) : points = [];

  final List<Point> points;
  final Size? screenSize;
  String? _drawId;

  void addPoint(String drawId, Point point) {
    if (_drawId != null && _drawId != drawId) return;

    points.add(point);
  }

  void clear(String nextDrawId) {
    _drawId = nextDrawId;
    points.clear();
  }
}
