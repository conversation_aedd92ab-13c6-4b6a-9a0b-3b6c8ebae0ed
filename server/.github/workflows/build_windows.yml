on: workflow_dispatch

name: Build Windows

# Cancel jobs and just run the last one
concurrency:
  group: ${{ github.head_ref }}-deploy-to-staging
  cancel-in-progress: true

jobs:
  windows:
    runs-on:  windows-latest
    steps:
      - uses: actions/checkout@v3
      - name: Prepare flutter version from pubspec.yml
        run: |
          $content = Get-Content -Path pubspec.yaml
          $match = [regex]::match($content, 'flutter: (\d+\.\d+\.\d+)')
          echo "FLUTTER_VERSION=$($match.Groups[1].Value)" | Out-File -FilePath $env:GITHUB_ENV -Append
        shell: pwsh

      - uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{env.FLUTTER_VERSION}}
          cache: false

      - run: flutter config --enable-windows-desktop
      - run: flutter pub get
      - run: flutter build windows --release

      - name: Archive
        uses: actions/upload-artifact@v4
        with:
          name: windows
          path: build\windows\x64\runner\Release\

