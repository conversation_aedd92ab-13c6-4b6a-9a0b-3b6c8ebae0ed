package com.example.spen.common

import android.content.Context
import android.util.Log
import com.xiao.rxbonjour.RxBonjour
import io.reactivex.disposables.Disposable
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.suspendCancellableCoroutine
import java.lang.ref.WeakReference
import java.nio.charset.StandardCharsets
import kotlin.coroutines.resumeWithException

const val TAG = "[discovery]"

class NetworkDiscovery(context: Context) {
    var weakContext = WeakReference(context)
    private var discoveryDisposable: Disposable? = null

    @OptIn(ExperimentalCoroutinesApi::class)
    suspend fun search(): Remote? {
        val context = weakContext.get() ?: return null

        discoveryDisposable?.dispose()

        return suspendCancellableCoroutine { continuation ->
            try {
                discoveryDisposable = RxBonjour
                    .startDiscovery(context, "_http._tcp")
                    .subscribe({ service ->
                        if (!service.isAdded) return@subscribe

                        var remote: Remote? = null

                        val hostAddressBytes = service.attributes["ip"]
                        if (hostAddressBytes != null) {
                            val hostAddress =  String(hostAddressBytes, StandardCharsets.UTF_8)
                            remote = Remote(hostAddress, service.servicePort)
                            Log.d(TAG, "$TAG Server discovered host: $hostAddress")
                        } else {
                            Log.e(TAG, "$TAG Server discovered without IP address")
                        }

                        continuation.resume(remote, onCancellation = {
                            discoveryDisposable?.dispose()
                        })

                        discoveryDisposable?.dispose()
                    },
                        { error ->
                            Log.e(TAG, "$TAG Error: $error", error)

                            continuation.resumeWithException(error)
                        }
                    )

                continuation.invokeOnCancellation {
                    discoveryDisposable?.dispose()
                }
            } catch (e: Exception) {
                Log.e(TAG, "$TAG Error: $e", e)
                continuation.resumeWithException(e)
            }
        }
    }
}

class Remote(val host: String, val port: Int)