package com.example.spen.di

import android.content.Context
import com.example.spen.common.api.DrawRepository
import com.example.spen.common.NetworkDiscovery
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
object AppModule {
    @Provides
    fun provideDrawRepository(): DrawRepository {
        return DrawRepository()
    }

    @Provides
    fun provideNetworkDiscovery(@ApplicationContext context: Context): NetworkDiscovery {
        return NetworkDiscovery(context)
    }
}