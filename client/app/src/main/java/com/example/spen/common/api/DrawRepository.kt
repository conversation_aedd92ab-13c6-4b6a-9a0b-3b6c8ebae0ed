package com.example.spen.common.api

import android.util.Log
import android.util.Size
import com.google.gson.Gson
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import java.net.HttpURLConnection
import java.net.URL

class DrawRepository(private val ioDispatcher: CoroutineDispatcher = Dispatchers.IO) {
    private var serverIp: String? = null
    private var serverPort: Int? = null
    private val client = OkHttpClient()

    val gson = Gson()

    fun setServer(ip: String, port: Int) {
        serverIp = ip
        serverPort = port
    }

    suspend fun send(drawId: String, point: Point, screenSize: Size) {
        return safeApiCall {
            val json = gson.toJson(
                mapOf(
                    "payload" to mapOf(
                        "draw_id" to drawId,
                        "point" to point.toMap(),
                        "screen_size" to mapOf(
                            "width" to screenSize.width.toDouble(),
                            "height" to screenSize.height.toDouble()
                        )
                    )
                )
            )

            sendRequest("draw_single", json)
        }
    }

    suspend fun sendTouchUp(drawId: String) = safeApiCall {
        val json = gson.toJson(
            mapOf(
                "payload" to mapOf(
                    "draw_id" to drawId,
                )
            )
        )

        sendRequest(DrawAction.TOUCH_UP.name.lowercase(), json)
    }

    suspend fun sendTouchDown(drawId: String) = safeApiCall {
        val json = gson.toJson(
            mapOf(
                "payload" to mapOf(
                    "draw_id" to drawId,
                )
            )
        )

        sendRequest(DrawAction.TOUCH_DOWN.name.lowercase(), json)
    }

    suspend fun sendAction(drawAction: DrawAction) = safeApiCall {
        sendRequest(drawAction.name.lowercase(), null)
    }

    private fun logError(responseCode: Int) {
        if (responseCode >= HttpURLConnection.HTTP_BAD_REQUEST) {
            Log.e("HTTP", "clean failed : HTTP error code : $responseCode")
        }
    }

    private fun sendRequest(resource: String, json: String?): Int {
        try {
            val url = URL("http://$serverIp:$serverPort/$resource")

            val requestBody =
                (json ?: "").toRequestBody("application/json; charset=utf-8".toMediaType())

            // Build the POST request
            val request = Request.Builder()
                .url(url)
                .post(requestBody)
                .build()

            try {
                val response = client.newCall(request).execute()
                val responseCode = response.code

                logError(responseCode)
                return responseCode
            } catch (e: Exception) {
                e.printStackTrace()
            }
        } catch (e: Exception) {
            //TODO: Return Result monad instead
            Log.e("HTTP", "Request failed: ${e.message}", e)
        }

        return -1
    }

    private suspend fun <T> safeApiCall(apiCall: suspend () -> T): T = withContext(ioDispatcher) {
        try {
            apiCall()
        } catch (e: Exception) {
            Log.e("HTTP", "Request failed: ${e.message}", e)
            throw e // TODO: Handle error
        }
    }
}

enum class DrawAction {
    CLEAN, SHOW, HIDE, SHOW_WHITEBOARD, HIDE_WHITEBOARD, TOUCH_UP, TOUCH_DOWN
}