package com.example.spen.common.api

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.colorspace.ColorSpaces
import androidx.compose.ui.graphics.toArgb

data class Point(val x: Float, val y: Float, val color: Color, val pressure: Float) {
    //TODO: use a serializer instead of manual serialization
    fun toMap(): Map<String, Any> {
        return mapOf(
            "x" to x,
            "y" to y,
            "color" to serializeComposeColorToHex(color),
            "pressure" to pressure
        )
    }
}


fun serializeComposeColorToHex(color: Color): String {
    // Convert the Compose Color to ARGB in the sRGB color space
    val colorInt = color.convert(ColorSpaces.Srgb).toArgb()
    // Format as #AARRGGBB
    return String.format("#%08X", colorInt)
}