package com.example.spen.features.home

import androidx.compose.ui.graphics.Color
import com.example.spen.common.api.Point

data class HomeStatus(
    val connectionLabel: String = "Discovering...",
    val colors: List<Color>,
    val currentColor: Color,
    val allowHandDrawing: <PERSON>olean,
    val allowWhiteboard: <PERSON>olean,
    val points: List<List<Point>>,
    val sessionPoints: List<Point>,
    val isColorDialogVisible: Boolean,
)