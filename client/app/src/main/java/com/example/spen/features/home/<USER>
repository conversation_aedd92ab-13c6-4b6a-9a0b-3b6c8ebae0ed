package com.example.spen.features.home

import android.os.Build
import android.os.Bundle
import android.util.DisplayMetrics
import android.util.Size
import android.view.MotionEvent
import android.view.WindowInsets
import android.view.WindowMetrics
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.sharp.Create
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.input.pointer.pointerInteropFilter
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.spen.common.api.DrawAction
import com.example.spen.common.api.Point
import dagger.hilt.android.AndroidEntryPoint
import io.mhssn.colorpicker.ColorPickerDialog
import io.mhssn.colorpicker.ColorPickerType

@AndroidEntryPoint
class HomeActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val screenSize = screenSize()
        setContent { DrawingApp(screenSize) }
    }

    private fun screenSize(): Size {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val windowMetrics: WindowMetrics = windowManager.currentWindowMetrics
            val insets =
                windowMetrics.windowInsets.getInsetsIgnoringVisibility(WindowInsets.Type.systemBars())
            Size(
                windowMetrics.bounds.width() - insets.left - insets.right,
                windowMetrics.bounds.height() - insets.top - insets.bottom
            )
        } else {
            val displayMetrics = DisplayMetrics()
            windowManager.defaultDisplay.getMetrics(displayMetrics)

            Size(displayMetrics.widthPixels, displayMetrics.heightPixels)
        }
    }
}

@Composable
fun DrawingApp(size: Size) {
    val colorScheme = MaterialTheme.colorScheme

    val viewModel: DrawingViewModel =
        hiltViewModel<DrawingViewModel, DrawingViewModel.Factory> { factory ->
            val initialColors = listOf(
                colorScheme.primary,
                colorScheme.inversePrimary,
                colorScheme.secondary,
                colorScheme.tertiary,
            )

            factory.create(initialColors = initialColors)
        }

    val status by viewModel.status.collectAsState()

    Body(
        viewModel,
        status.currentColor,
        status.points,
        status.sessionPoints,
        status.allowHandDrawing,
        onAllowHandDrawingChange = { viewModel.onHandDrawingChange(it) },
        status.isColorDialogVisible,
        onColorSelected = { index, color -> viewModel.onColorSelected(index, color) },
        onColorDismissed = { viewModel.onColorDialogDismissed() },
        status.allowWhiteboard,
        onAllowWhiteboardChange = { viewModel.onWhiteBoardChange(isEnabled = it) },
        onActionDown = { viewModel.onActionDown() },
        onActionUp = { viewModel.onActionUp() },
        onMove = { viewModel.onMove(it, screenSize = size) },
        colors = status.colors,
    )
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun Body(
    viewModel: DrawingViewModel,
    currentColor: Color,
    points: List<List<Point>>,
    currentPoints: List<Point>,
    isHandDrawingEnabled: Boolean,
    onAllowHandDrawingChange: (Boolean) -> Unit,
    isColorDialogVisible: Boolean,
    onColorSelected: (Int, Color) -> Unit,
    onColorDismissed: () -> Unit,
    isWhiteBoardEnabled: Boolean,
    onAllowWhiteboardChange: (Boolean) -> Unit,
    onActionDown: () -> Unit,
    onActionUp: () -> Unit,
    onMove: (Point) -> Unit,
    colors: List<Color>,
) {
    Box(modifier = Modifier.fillMaxSize()) {
        DrawingCanvas(
            currentColor = currentColor,
            points = points,
            sessionPoints = currentPoints,
            isHandDrawingEnabled = isHandDrawingEnabled,
            onActionDown = onActionDown,
            onActionUp = onActionUp,
            onMove = onMove,
        )
        ColorPickerDialog(
            show = isColorDialogVisible,
            type = ColorPickerType.Ring(),
            properties = DialogProperties(),
            onDismissRequest = { onColorDismissed() },
            onPickedColor = {
                val colorIndex = viewModel.status.value.colors.indexOf(currentColor)
                onColorSelected(colorIndex, it)
            },
        )
        ColorButtons(
            modifier = Modifier.align(Alignment.CenterStart),
            colors = colors,
            onColorSelected = onColorSelected,
            currentColor = currentColor,
        )
        Tools(
            viewModel,
            modifier = Modifier.align(Alignment.CenterEnd),
            isHandDrawingEnabled = isHandDrawingEnabled,
            onAllowHandDrawingChange = onAllowHandDrawingChange,
            isWhiteBoardEnabled = isWhiteBoardEnabled,
            onAllowWhiteboardChange = onAllowWhiteboardChange,
        )
        ConnectionStatus(
            viewModel,
            modifier = Modifier.align(Alignment.BottomCenter),
        )
    }
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun DrawingCanvas(
    currentColor: Color,
    points: List<List<Point>>,
    sessionPoints: List<Point>,
    onActionDown: () -> Unit,
    onActionUp: () -> Unit,
    onMove: (Point) -> Unit,
    isHandDrawingEnabled: Boolean,
) {
    Canvas(modifier = Modifier
        .fillMaxSize()
        .pointerInteropFilter { event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    onActionDown()
                }

                MotionEvent.ACTION_MOVE -> {
                    val toolType = event.getToolType(0)

                    if (toolType == MotionEvent.TOOL_TYPE_STYLUS || (isHandDrawingEnabled && toolType == MotionEvent.TOOL_TYPE_FINGER)) {
                        val point = Point(event.x, event.y, currentColor, event.getPressure(0))
                        onMove(point)
                    }
                }

                MotionEvent.ACTION_UP -> {
                    onActionUp()
                }
            }
            true
        }) {
        points.forEach { drawLines(it) }
        drawLines(sessionPoints)
    }
}

@Composable
fun ColorButtons(
    modifier: Modifier = Modifier,
    colors: List<Color> = listOf(Color.Red, Color.Green, Color.Blue, Color.Magenta),
    currentColor: Color,
    onColorSelected: (Int, Color) -> Unit, // A function to update the currentColor
) {
    Card(
        modifier = modifier, elevation = CardDefaults.cardElevation(2.dp)
    ) {
        Column(
            modifier = modifier.padding(16.dp),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(text = "Color", style = MaterialTheme.typography.headlineSmall)
            Spacer(modifier = Modifier.height(8.dp))
            colors.forEachIndexed { i, color ->
                Button(
                    onClick = { onColorSelected(i, color) },
                    modifier = Modifier.size(50.dp),
                    colors = ButtonDefaults.buttonColors(containerColor = color),
                    shape = CircleShape,
                    contentPadding = PaddingValues(0.dp)
                ) {
                    if (color == currentColor) {
                        Icon(
                            imageVector = Icons.Sharp.Create,
                            contentDescription = "Selected",
                            tint = Color.White
                        )
                    }
                }
                Spacer(modifier = Modifier.height(4.dp))
            }
        }
    }
}

@Composable
fun Tools(
    viewModel: DrawingViewModel,
    modifier: Modifier = Modifier,
    isHandDrawingEnabled: Boolean,
    onAllowHandDrawingChange: (Boolean) -> Unit,
    isWhiteBoardEnabled: Boolean,
    onAllowWhiteboardChange: (Boolean) -> Unit,
) {
    Card(
        modifier = modifier, elevation = CardDefaults.cardElevation(2.dp)
    ) {
        Column(
            modifier = modifier.padding(16.dp),
        ) {
            Text(text = "Allow hand drawing", style = MaterialTheme.typography.headlineSmall)
            Spacer(modifier = Modifier.height(4.dp))
            Switch(checked = isHandDrawingEnabled, onCheckedChange = onAllowHandDrawingChange)
            Spacer(modifier = Modifier.height(8.dp))

            Text(text = "Allow whiteboard", style = MaterialTheme.typography.headlineSmall)
            Spacer(modifier = Modifier.height(4.dp))
            Switch(checked = isWhiteBoardEnabled, onCheckedChange = onAllowWhiteboardChange)
            Spacer(modifier = Modifier.height(8.dp))

            Text(text = "Actions", style = MaterialTheme.typography.headlineSmall)
            Spacer(modifier = Modifier.height(4.dp))
            Button(
                onClick = { viewModel.onActionActivate(DrawAction.SHOW) },
            ) {
                Text("Show")
            }
            Spacer(modifier = Modifier.height(4.dp))

            Button(
                onClick = { viewModel.onActionActivate(DrawAction.HIDE) },
            ) {
                Text("Hide")
            }
            Spacer(modifier = Modifier.height(4.dp))

            Button(
                onClick = { viewModel.onActionActivate(DrawAction.CLEAN) },
            ) {
                Text("Clear")
            }
        }
    }
}

@Composable
fun ConnectionStatus(
    viewModel: DrawingViewModel,
    modifier: Modifier = Modifier,
) {
    val status by viewModel.status.collectAsState()
    val connectionLabel by remember { derivedStateOf { status.connectionLabel } }

    Card(
        modifier = modifier, elevation = CardDefaults.cardElevation(2.dp)
    ) {
        Column(
            modifier = modifier.padding(16.dp),
        ) {
            Text(text = "Server Status", style = MaterialTheme.typography.headlineSmall)
            Text(
                modifier = Modifier.padding(8.dp),
                color = Color.Black,
                text = connectionLabel,
                style = MaterialTheme.typography.bodyLarge,
            )
        }
    }
}


fun DrawScope.drawLines(points: List<Point>) {
    if (points.size > 1) {
        for (i in 0 until points.size - 1) {
            val start = Offset(points[i].x, points[i].y)
            val end = Offset(points[i + 1].x, points[i + 1].y)
            drawLine(color = points[i].color, start = start, end = end, strokeWidth = 5f)
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ColorButtonsPreview() {
    ColorButtons(
        onColorSelected = { _, _ -> },
        currentColor = Color.Green,
        colors = listOf(Color.Red, Color.Green, Color.Blue, Color.Magenta)
    )
}