package com.example.spen.features.home

import android.R.attr.port
import android.util.Log
import android.util.Log.e
import android.util.Size
import androidx.compose.ui.graphics.Color
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.spen.common.NetworkDiscovery
import com.example.spen.common.TAG
import com.example.spen.common.api.DrawAction
import com.example.spen.common.api.DrawRepository
import com.example.spen.common.api.Point
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import java.util.UUID

@HiltViewModel(assistedFactory = DrawingViewModel.Factory::class)
class DrawingViewModel @AssistedInject constructor(
    private val drawRepository: DrawRepository,
    private val networkDiscovery: NetworkDiscovery,
    @Assisted initialColors: List<Color>,
) : ViewModel() {
    @AssistedFactory
    interface Factory {
        fun create(initialColors: List<Color>): DrawingViewModel
    }

    val status = MutableStateFlow(
        HomeStatus(
            connectionLabel = "Discovering...",
            colors = initialColors,
            currentColor = initialColors.first(),
            allowHandDrawing = true,
            allowWhiteboard = true,
            points = emptyList(),
            sessionPoints = emptyList(),
            isColorDialogVisible = false
        )
    )

    private var sessionId = "1"

    init {
        viewModelScope.launch(Dispatchers.IO) {
            discoverServer()
        }
    }

    fun onActionActivate(drawAction: DrawAction) {
        viewModelScope.launch(Dispatchers.IO) {
            drawRepository.sendAction(drawAction)
        }

        if (drawAction == DrawAction.CLEAN) {
            status.value = status.value.copy(points = emptyList())
        }
    }

    fun onWhiteBoardChange(isEnabled: Boolean) = viewModelScope.launch(Dispatchers.IO) {
        val drawAction: DrawAction =
            if (isEnabled) DrawAction.SHOW_WHITEBOARD else DrawAction.HIDE_WHITEBOARD

        drawRepository.sendAction(drawAction)
        status.value = status.value.copy(allowWhiteboard = isEnabled)
    }

    fun onActionDown() = viewModelScope.launch(Dispatchers.IO) {
        drawRepository.sendTouchDown(sessionId)
    }

    fun onActionUp() = viewModelScope.launch(Dispatchers.IO) {
        sessionId = UUID.randomUUID().toString()
        drawRepository.sendTouchUp(sessionId)

        status.value = status.value.copy(
            points = status.value.points + listOf(status.value.sessionPoints),
            sessionPoints = emptyList()
        )
    }

    fun onMove(point: Point, screenSize: Size) {
        viewModelScope.launch(Dispatchers.IO) {
            drawRepository.send(sessionId, point, screenSize)
        }

        val points = status.value.sessionPoints + point
        status.value = status.value.copy(sessionPoints = points)
    }

    fun onColorSelected(index: Int, color: Color) {
        if (status.value.currentColor == color) {
            status.value =
                status.value.copy(isColorDialogVisible = !status.value.isColorDialogVisible)
        } else {
            status.value = status.value.copy(
                currentColor = color,
                colors = status.value.colors.mapIndexed { i, c -> if (i == index) color else c }
            )
        }
    }

    fun onHandDrawingChange(bool: Boolean) {
        status.value = status.value.copy(allowHandDrawing = bool)
    }

    fun onColorDialogDismissed() {
        status.value = status.value.copy(isColorDialogVisible = false)
    }

    private suspend fun discoverServer() {
        Log.d(TAG, "Discovering server...")
        try {
            val remote = networkDiscovery.search()

            if (remote != null) {
                val port = remote.port
                val host = remote.host

                drawRepository.setServer(host, port)
                status.value = status.value.copy(connectionLabel = "Connected to $host$port")

                Log.d(TAG, "Server discovered host: $host$port")
            } else {
                status.value = status.value.copy(connectionLabel = "No server found")
                Log.d(TAG, "No server found")
            }
        } catch (e: Exception) {
            status.value = status.value.copy(connectionLabel = "Error: ${e.message}")
            Log.e(TAG, "Error: ${e.message}", e)
        }
    }
}
