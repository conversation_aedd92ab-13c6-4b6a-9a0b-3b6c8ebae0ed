package com.example.spen.features.home

import androidx.compose.ui.graphics.Color
import com.example.spen.common.api.DrawAction
import com.example.spen.common.api.DrawRepository
import com.example.spen.common.NetworkDiscovery
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest

@ExperimentalCoroutinesApi
class DrawingViewModelTest : BehaviorSpec({
    val drawRepository = mockk<DrawRepository>(relaxed = true)
    val networkDiscovery = mockk<NetworkDiscovery>()
    val colors = listOf(Color.Red, Color.Blue, Color.Green)

    fun subject() = DrawingViewModel(drawRepository, networkDiscovery, colors)

    given(".onActionActivated") {
        DrawAction.entries.toTypedArray().onEach { action ->
            `when`("with drawAction $action") {
                then("sends $action to repository") {
                    runTest {
                        subject().onActionActivate(action)

                        coVerify { drawRepository.sendAction(action) }
                    }
                }
            }
        }
    }

    given(".onWhiteBoardChanged") {
        `when`("with enabled") {
            then("sends SHOW_WHITEBOARD action to repository") {
                runTest {
                    subject().onWhiteBoardChange(isEnabled = true)

                    coVerify { drawRepository.sendAction(DrawAction.SHOW_WHITEBOARD) }
                }
            }
        }

        `when`("with no enabled") {
            then("sends HIDE_WHITEBOARD action to repository") {
                runTest {
                    subject().onWhiteBoardChange(isEnabled = false)

                    coVerify { drawRepository.sendAction(DrawAction.HIDE_WHITEBOARD) }
                }
            }
        }
    }
})
